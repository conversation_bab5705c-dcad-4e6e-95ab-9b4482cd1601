mod screen_state;
use tauri::{<PERSON>, WebviewWindow, WindowEvent};

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
fn is_screen_active() -> bool {
    screen_state::is_screen_active()
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_http::init())
        .plugin(tauri_plugin_fs::init())
        .setup(|app| {
            let window = app.get_webview_window("main").unwrap();
            setup_window_events(&window);
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![greet, is_screen_active])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

fn setup_window_events(window: &WebviewWindow) {
    let window_clone = window.clone();

    window.on_window_event(move |event| match event {
        WindowEvent::CloseRequested { api, .. } => {
            println!("🚫 窗口关闭请求被拦截");
            api.prevent_close();
            let _ = window_clone.hide();
            println!("👻 窗口已隐藏到托盘");
        }
        WindowEvent::Focused(is_focused) => {
            if *is_focused {
                println!("🎯 窗口获得焦点");
            } else {
                println!("😴 窗口失去焦点");
            }
        }
        _ => {}
    });
}
