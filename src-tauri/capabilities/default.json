{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "default", "description": "Capability for the main window", "windows": ["main"], "permissions": ["core:default", "opener:default", "http:default", "fs:default", "fs:write-all", "fs:read-all", "fs:allow-exists", "fs:allow-mkdir", "fs:allow-app-read-recursive", "fs:allow-app-write-recursive", "fs:scope", "fs:scope-app", "fs:scope-desktop", "fs:scope-document", "fs:scope-download", "fs:scope-home", "fs:scope-temp", "fs:scope-resource", "fs:allow-app-meta", "fs:allow-app-read", "fs:allow-app-write", "core:window:allow-show", "core:window:allow-set-focus", {"identifier": "http:default", "allow": [{"url": "https://relay03.gaccode.com/api/**"}]}]}