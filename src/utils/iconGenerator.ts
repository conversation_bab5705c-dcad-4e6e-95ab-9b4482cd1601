import { writeFile, exists, mkdir } from "@tauri-apps/plugin-fs";
import { appDataDir } from "@tauri-apps/api/path";

export type IconType = "balance" | "error" | "default";

/**
 * 动态生成带数字的托盘图标
 */
export class IconGenerator {
  private static canvas: HTMLCanvasElement | null = null;
  private static ctx: CanvasRenderingContext2D | null = null;
  private static iconDir: string | null = null;
  private static fontSize: number = 32;

  private static initCanvas(): void {
    if (!this.canvas) {
      this.canvas = document.createElement("canvas");
      this.canvas.width = 80;
      this.canvas.height = 48;
      this.ctx = this.canvas.getContext("2d");

      if (this.ctx) {
        this.ctx.imageSmoothingEnabled = true;
        this.ctx.imageSmoothingQuality = "high";
      }
    }
  }

  private static async getIconDir(): Promise<string> {
    if (!this.iconDir) {
      const dataDir = await appDataDir();
      if (!(await exists(`${dataDir}/icons`))) {
        await mkdir(`${dataDir}/icons`, { recursive: true });
      }
      this.iconDir = `${dataDir}/icons`;
    }
    return this.iconDir;
  }

  /**
   * 统一的图标生成方法
   * @param type 图标类型
   * @param balance 余额数字（仅在类型为 'balance' 时使用）
   * @returns 图标文件路径
   */
  static async generateIcon(
    type: IconType,
    balance?: number | null
  ): Promise<string> {
    this.initCanvas();
    if (!this.ctx || !this.canvas) {
      throw new Error("Failed to initialize canvas");
    }

    const ctx = this.ctx;
    const width = this.canvas.width;
    const height = this.canvas.height;

    // 清空画布
    ctx.clearRect(0, 0, width, height);

    // DEBUG
    // ctx.fillStyle = "#000000";
    // ctx.fillRect(0, 0, width, height);

    // 设置通用样式
    ctx.textAlign = "left";
    ctx.textBaseline = "middle";

    // 根据类型设置颜色和内容
    let color: string;
    let bottomText: string;
    let filename: string;

    switch (type) {
      case "balance":
        color = "#ffffff";
        if (balance !== null && balance !== undefined) {
          if (balance >= 10000) {
            bottomText = Math.floor(balance / 1000) + "K";
          } else {
            bottomText = balance.toString();
          }
          filename = `tray-${balance}.png`;
        } else {
          bottomText = "?";
          filename = "tray-unknown.png";
        }
        break;

      case "error":
        color = "#ff4444";
        bottomText = "9999";
        filename = "tray-error.png";
        break;

      case "default":
        color = "#888888";
        bottomText = "---";
        filename = "tray-default.png";
        break;
    }

    ctx.fillStyle = color;

    // 绘制上半部分 "GAC"
    ctx.font =
      'bold 18px -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif';
    ctx.fillText("GAC", 0, height / 2 - 13);

    // 绘制下半部分
    ctx.font = `${this.fontSize}px -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif`;
    ctx.fillText(bottomText, 0, height / 2 + 10);

    return await this.saveCanvasAsPNG(filename);
  }

  // 保持向后兼容的方法
  static async generateTrayIcon(balance: number | null): Promise<string> {
    return this.generateIcon("balance", balance);
  }

  static async generateErrorIcon(): Promise<string> {
    return this.generateIcon("error");
  }

  static async generateDefaultIcon(): Promise<string> {
    return this.generateIcon("default");
  }

  /**
   * 将 Canvas 保存为 PNG 文件
   * @param filename 文件名
   * @returns 文件路径
   */
  private static async saveCanvasAsPNG(filename: string): Promise<string> {
    if (!this.canvas) {
      throw new Error("Canvas not initialized");
    }

    // 获取 canvas 的 blob - 使用最高质量无损PNG
    const blob = await new Promise<Blob>((resolve) => {
      this.canvas!.toBlob(
        (blob) => {
          if (blob) resolve(blob);
        },
        "image/png",
        1.0
      ); // 质量设为1.0（最高质量）
    });

    // 转换为 Uint8Array
    const arrayBuffer = await blob.arrayBuffer();
    const uint8Array = new Uint8Array(arrayBuffer);

    // 确保图标目录存在并保存文件
    const iconDir = await this.getIconDir();
    const filePath = `${iconDir}/${filename}`;

    try {
      await writeFile(filePath, uint8Array);
      return filePath;
    } catch (error) {
      console.error("Failed to save icon file:", error);
      throw error;
    }
  }
}
