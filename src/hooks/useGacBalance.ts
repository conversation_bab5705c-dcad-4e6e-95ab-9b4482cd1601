import { useQuery } from "@tanstack/react-query";
import { ApiService } from "../services/api";
import type { GacBalanceResponse } from "../types";

export function useGacBalance(token: string | null, enabled: boolean = true) {
  return useQuery<GacBalanceResponse, Error>({
    queryKey: ["gac-balance", token],
    queryFn: () => {
      if (!token) {
        throw new Error("Token is required");
      }
      return ApiService.getBalance(token);
    },
    enabled: enabled && !!token,
    staleTime: 30 * 1000, // 30秒内认为数据是新鲜的
    refetchInterval: 60 * 1000, // 每分钟自动刷新
    refetchIntervalInBackground: false, // 窗口失焦时停止轮询
    retry: false,
    // retry: (failureCount, error) => {
    //   // API 认证错误不重试
    //   if (error.message.includes('401') || error.message.includes('403')) {
    //     return false;
    //   }
    //   return failureCount < 3;
    // },
    // retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}
