import { useLocalStorage } from '@mantine/hooks';

const TOKEN_KEY = 'gac_auth_token';

export function useGacToken() {
  const [token, setToken] = useLocalStorage<string | null>({
    key: TOKEN_KEY,
    defaultValue: null,
  });

  const saveToken = (newToken: string) => {
    setToken(newToken.trim());
  };

  const removeToken = () => {
    setToken(null);
  };

  const hasToken = !!token;

  return {
    token,
    saveToken,
    removeToken,
    hasToken,
  };
}