import { useEffect, useRef } from "react";
import { MantineProvider, Container, Stack, Title, Text } from "@mantine/core";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { TrayService } from "./services/tray";
import { TokenSettings } from "./components/TokenSettings";
import "./App.css";
import "@mantine/core/styles.css";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 30 * 1000,
      refetchOnWindowFocus: false,
    },
  },
});

TrayService.initialize().catch(console.error);

function App() {
  useEffect(() => {
    // 监听刷新事件
    const handleRefresh = () => {
      queryClient.invalidateQueries({ queryKey: ["gac-balance"] });
    };
    window.addEventListener("refresh-balance", handleRefresh);

    return () => {
      window.removeEventListener("refresh-balance", handleRefresh);
    };
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <MantineProvider>
        <Container size="sm" py="md">
          <Stack gap="lg">
            <Stack gap="xs" align="center">
              <Title order={2}>GAC Info</Title>
              <Text size="sm" c="dimmed">
                GAC 余额监控工具
              </Text>
            </Stack>

            <TokenSettings />
          </Stack>
        </Container>
      </MantineProvider>
    </QueryClientProvider>
  );
}

export default App;
