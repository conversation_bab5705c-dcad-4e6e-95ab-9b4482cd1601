import { TrayIcon } from "@tauri-apps/api/tray";
import { Menu } from "@tauri-apps/api/menu";
import { getCurrentWebviewWindow } from "@tauri-apps/api/webviewWindow";
import { IconGenerator } from "../utils/iconGenerator";

export class TrayService {
  static trayIcon: TrayIcon | null = null;
  private static currentBalance: number | null = null;

  static async initialize(): Promise<TrayService> {
    try {
      // 创建托盘菜单
      const menu = await Menu.new({
        items: [
          {
            id: "show",
            text: "显示窗口",
            action: () => this.showWindow(),
          },
          {
            id: "refresh",
            text: "刷新余额",
            action: () => this.refreshBalance(),
          },
          {
            id: "separator1",
            text: "",
          },
          {
            id: "quit",
            text: "退出",
            action: () => this.quit(),
          },
        ],
      });

      // 生成默认图标
      const iconPath = await IconGenerator.generateDefaultIcon();

      // 先移除旧的托盘图标
      if (await TrayIcon.getById("gac-info-tray")) {
        await TrayIcon.removeById("gac-info-tray");
      }

      // 创建托盘图标
      this.trayIcon = await TrayIcon.new({
        id: "gac-info-tray",
        icon: iconPath,
        tooltip: "GAC Info - 点击查看余额",
        menu,
        action: (event) => {
          if (event.type === "Click") {
            // this.showWindow();
          }
        },
      });

      console.log("Tray icon initialized successfully");
    } catch (error) {
      console.error("Failed to initialize tray icon:", error);
      throw error;
    }
    return this;
  }

  static async updateBalance(balance: number | null): Promise<void> {
    if (!this.trayIcon) {
      console.warn("Tray icon not initialized");
      // 先移除旧的托盘图标
      if (await TrayIcon.getById("gac-info-tray")) {
        console.log("remove old tray icon");
        await TrayIcon.removeById("gac-info-tray");
      } else {
        console.log("tray icon not exist");
      }
      await this.initialize();
      console.log("re-initialize tray icon");
      return;
    }

    try {
      this.currentBalance = balance;

      // 生成带数字的图标
      const iconPath = await IconGenerator.generateTrayIcon(balance);

      // 更新图标
      await this.trayIcon.setIcon(iconPath);

      // 更新提示文本
      const balanceText = balance !== null ? `${balance} credits` : "未知";
      await this.trayIcon.setTooltip(`GAC Info - 余额: ${balanceText}`);

      console.log(`Tray icon updated with balance: ${balance}`);
    } catch (error) {
      console.error("Failed to update tray balance:", error);
    }
  }

  static async updateError(error: string): Promise<void> {
    if (!this.trayIcon) {
      console.warn("Tray icon not initialized");
      return;
    }

    try {
      // 生成错误图标
      const iconPath = await IconGenerator.generateErrorIcon();

      // 更新图标
      await this.trayIcon.setIcon(iconPath);

      // 更新提示文本
      await this.trayIcon.setTooltip(`GAC Info - 错误: ${error}`);

      console.log(`Tray icon updated with error: ${error}`);
    } catch (error) {
      console.error("Failed to update tray error:", error);
    }
  }

  static async resetToDefault(): Promise<void> {
    if (!this.trayIcon) {
      console.warn("Tray icon not initialized");
      return;
    }

    try {
      // 重置为默认图标
      const iconPath = await IconGenerator.generateDefaultIcon();

      await this.trayIcon.setIcon(iconPath);
      await this.trayIcon.setTooltip("GAC Info - 点击查看余额");

      this.currentBalance = null;
      console.log("Tray icon reset to default");
    } catch (error) {
      console.error("Failed to reset tray icon:", error);
    }
  }

  static getCurrentBalance(): number | null {
    return this.currentBalance;
  }

  private static async showWindow(): Promise<void> {
    try {
      const window = getCurrentWebviewWindow();
      await window.show();
      await window.setFocus();
    } catch (error) {
      console.error("Failed to show window:", error);
    }
  }

  private static refreshBalance(): void {
    // 触发余额刷新事件
    window.dispatchEvent(new CustomEvent("refresh-balance"));
  }

  private static async quit(): Promise<void> {
    try {
      const { exit } = await import("@tauri-apps/plugin-process");
      await exit(0);
    } catch (error) {
      console.error("Failed to quit application:", error);
    }
  }

  static async destroy(): Promise<void> {
    if (this.trayIcon) {
      try {
        await this.trayIcon.close();
        this.trayIcon = null;
        this.currentBalance = null;
      } catch (error) {
        console.error("Failed to destroy tray icon:", error);
      }
    }
  }
}
