import { fetch } from "@tauri-apps/plugin-http";
import type { GacBalanceResponse } from "../types";

export class ApiService {
  private static readonly API_URL =
    "https://relay03.gaccode.com/api/credits/balance";

  static async getBalance(token: string): Promise<GacBalanceResponse> {
    if (!token) {
      throw new Error("Token is required");
    }

    try {
      const response = await fetch(this.API_URL, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        connectTimeout: 2000,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = (await response.json()) as GacBalanceResponse;
      console.log(data);
      return data;
    } catch (error) {
      console.error("Failed to fetch balance:", error);
      throw error;
    }
  }
}
