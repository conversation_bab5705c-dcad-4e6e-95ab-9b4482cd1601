import { useState, useEffect } from "react";
import {
  TextInput,
  Button,
  Stack,
  Text,
  Alert,
  Group,
  ActionIcon,
  Tooltip,
} from "@mantine/core";
import { IconKey, IconTrash, IconRefresh } from "@tabler/icons-react";
import { useGacToken } from "../hooks/useGacToken";
import { useGacBalance } from "../hooks/useGacBalance";
import { TrayService } from "../services/tray";

export function TokenSettings() {
  const { token, saveToken, removeToken, hasToken } = useGacToken();
  const [tokenInput, setTokenInput] = useState("");

  const {
    data: balanceData,
    error,
    isLoading,
    refetch,
    dataUpdatedAt,
    isRefetching,
  } = useGacBalance(token);

  // 当余额数据更新时，同步到托盘
  useEffect(() => {
    if (balanceData?.balance !== undefined) {
      TrayService.updateBalance(balanceData.balance).catch(console.error);
    }
  }, [balanceData?.balance, TrayService.trayIcon]);

  // 当出现错误时，同步到托盘
  useEffect(() => {
    if (error) {
      const errorMessage = `获取余额失败: ${error.message}`;
      TrayService.updateError(errorMessage).catch(console.error);
    }
  }, [error]);

  const handleSetToken = async () => {
    if (!tokenInput.trim()) return;
    saveToken(tokenInput.trim());
    setTokenInput("");
  };

  const handleRemoveToken = async () => {
    removeToken();
    await TrayService.resetToDefault();
  };

  const handleRefresh = () => {
    refetch();
  };

  return (
    <Stack gap="md">
      <Text size="lg" fw={600}>
        Token 设置
      </Text>

      {hasToken ? (
        <Stack gap="sm">
          <Group justify="space-between">
            <Text size="sm" c="dimmed">
              Token 已设置 (***{token?.slice(-8)})
            </Text>
            <Group gap="xs">
              <Tooltip label="刷新余额">
                <ActionIcon
                  variant="light"
                  onClick={handleRefresh}
                  loading={isLoading || isRefetching}
                >
                  <IconRefresh size={16} />
                </ActionIcon>
              </Tooltip>
              <Tooltip label="删除 Token">
                <ActionIcon
                  variant="light"
                  color="red"
                  onClick={handleRemoveToken}
                >
                  <IconTrash size={16} />
                </ActionIcon>
              </Tooltip>
            </Group>
          </Group>

          {balanceData?.balance !== undefined && (
            <Alert color="green" title="当前余额">
              {balanceData.balance} credits
            </Alert>
          )}

          {dataUpdatedAt && (
            <Text size="xs" c="dimmed">
              最后更新: {new Date(dataUpdatedAt).toLocaleString()}
            </Text>
          )}

          <Text size="xs" c="blue">
            ✓ 自动轮询已启用 (每分钟更新)
          </Text>
        </Stack>
      ) : (
        <Stack gap="sm">
          <TextInput
            label="Authorization Token"
            placeholder="请输入 Bearer Token"
            value={tokenInput}
            onChange={(e) => setTokenInput(e.target.value)}
            leftSection={<IconKey size={16} />}
            onKeyDown={(e) => {
              if (e.key === "Enter") {
                handleSetToken();
              }
            }}
          />
          <Button
            onClick={handleSetToken}
            disabled={!tokenInput.trim()}
            loading={isLoading}
          >
            设置 Token
          </Button>
        </Stack>
      )}

      {error && (
        <Alert color="red" title="错误">
          {error.message}
        </Alert>
      )}
    </Stack>
  );
}
