# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概览

这是一个基于 Tauri + React + TypeScript 的桌面应用程序，用于监控 GAC 余额。应用程序具有系统托盘功能，可以定时获取余额信息。

## 核心架构

- **前端**: React + TypeScript + Vite + Mantine UI + TailwindCSS + TanStack Query
- **后端**: Rust + Tauri 2.0
- **状态管理**: TanStack Query (服务端状态) + Mantine hooks (本地状态)
- **构建工具**: Vite (前端) + Cargo (Rust)
- **包管理**: pnpm (前端依赖)

## 开发命令

### 开发
```bash
pnpm dev          # 启动开发服务器
pnpm tauri dev    # 启动 Tauri 开发环境（推荐）
```

### 构建
```bash
pnpm build        # 构建前端资源 (TypeScript 编译 + Vite 构建)
pnpm tauri build  # 构建完整的桌面应用程序
```

### 预览
```bash
pnpm preview      # 预览构建后的前端
```

## 关键文件结构

### 前端 (src/)
- `App.tsx` - 主应用组件，配置 QueryClient 和 Mantine Provider
- `services/api.ts` - GAC API 调用服务
- `services/tray.ts` - 系统托盘服务，支持动态图标生成
- `utils/iconGenerator.ts` - 托盘图标生成工具，用 Canvas 绘制带数字的图标
- `components/TokenSettings.tsx` - Token 设置组件
- `hooks/useGacToken.ts` - Token 管理 hook (使用 Mantine useLocalStorage)
- `hooks/useGacBalance.ts` - 余额查询 hook (使用 TanStack Query)
- `types.ts` - TypeScript 类型定义

### 后端 (src-tauri/)
- `src/main.rs` - Rust 应用入口点
- `src/lib.rs` - 核心 Rust 逻辑
- `tauri.conf.json` - Tauri 应用配置

## 配置要点

### Tauri 配置
- 默认窗口尺寸: 1400x800
- 启用系统托盘图标，动态显示余额数字
- 使用 HTTP 插件进行 API 调用
- 开发端口: 1420 (前端), 1421 (HMR)

### 托盘功能
- 使用 Canvas 动态生成带数字的图标（32x32 像素）
- 支持不同数字范围的自适应字体大小
- 错误状态显示红色感叹号图标
- 默认状态显示 GAC 字样的灰色图标

### API 集成
- GAC API 端点: `https://relay03.gaccode.com/api/credits/balance`
- 使用 Bearer Token 认证
- TanStack Query 自动处理轮询、缓存、错误重试
- 支持窗口失焦时停止轮询，窗口激活时恢复

## TypeScript 配置
- 启用严格模式
- 目标 ES2020
- JSX: react-jsx
- 模块解析: bundler 模式